import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:foodcorp_admin/shared/const.dart';

class Loan {
  final String docId;
  final String uid;
  final DateTime createdAt;
  // final String name;
  final num
      totalLoanAmt; //  abhi tak usko total kitne pay kiye admin ne user ko
  final num appliedLoanAmt;
  final num totalLoanPaid; // abhi tak usne kitne pay kiye without interest
  final num totalLoanDue;
  final String loanType;
  final bool isSettled;
  final DateTime? settledOn; // Nullable
  final bool isNew;
  final DateTime appliedOn;
  final num applicationNo;
  final DateTime? approvedOn; // Nullable
  final DateTime? processedOn; // Nullable
  final num share;
  final num totalInterestPaid;

  final String? designation;
  final num bAcNo;
  final String? appliedLoanAmtinWords;
  final String? loanReason;
  final String? bSign;
  final String? surityName1;
  final String? surityName2;
  final num? sAcNo1;
  final num? sAcNo2;
  // final String suritySign1;
  // final String suritySign2;
  // final String? address;
  // final num? balance;
  final DateTime? rejectionDate;
  final String? rejectionReason;
  final num? monthlyInstallmentAmt;

  final String? loanPreClosureReason;

  final String? pdfString;

  Loan(
      {this.monthlyInstallmentAmt,
      required this.rejectionDate,
      required this.rejectionReason,
      // required this.address,
      // required this.name,
      required this.designation,
      required this.bAcNo,
      required this.appliedLoanAmtinWords,
      required this.loanReason,
      required this.bSign,
      required this.surityName1,
      required this.surityName2,
      required this.sAcNo1,
      required this.sAcNo2,
      // required this.suritySign1,
      // required this.suritySign2,
      required this.docId,
      required this.uid,
      required this.createdAt,
      required this.totalLoanAmt,
      required this.appliedLoanAmt,
      required this.totalLoanPaid,
      required this.totalLoanDue,
      required this.loanType,
      required this.isSettled,
      this.settledOn, // Nullable
      required this.isNew,
      required this.appliedOn,
      required this.applicationNo,
      this.approvedOn, // Nullable
      this.processedOn, // Nullable
      required this.share,
      required this.totalInterestPaid,
      // required this.balance,
      this.loanPreClosureReason,
      required this.pdfString});

  factory Loan.fromJson(Map<String, dynamic> json) {
    return Loan(
      docId: json['docId'],
      uid: json['uid'],
      createdAt: DateTime.parse(json['createdAt']),
      totalLoanAmt: json['totalLoanAmt'],
      appliedLoanAmt: json['appliedLoanAmt'],
      totalLoanPaid: json['totalLoanPaid'],
      totalLoanDue: json['totalLoanDue'],
      loanType: json['loanType'],
      isSettled: json['isSettled'],
      settledOn: json['settledOn'] != null
          ? DateTime.parse(json['settledOn'])
          : null, // Nullable
      isNew: json['isNew'],
      appliedOn: DateTime.parse(json['appliedOn']),
      applicationNo: json['applicationNo'],
      approvedOn: json['approvedOn'] != null
          ? DateTime.parse(json['approvedOn'])
          : null, // Nullable
      processedOn: json['processedOn'] != null
          ? DateTime.parse(json['processedOn'])
          : null, // Nullable
      share: json['share'],
      totalInterestPaid: json['totalInterestPaid'],

      designation: json['designation'],
      bAcNo: json['bAcNo'],
      appliedLoanAmtinWords: json['appliedLoanAmtinWords'],
      loanReason: json['loanReason'],
      bSign: json['bSign'],
      surityName1: json['surityName1'],
      surityName2: json['surityName2'],
      sAcNo1: json['sAcNo1'],
      sAcNo2: json['sAcNo2'],
      // suritySign1: json['suritySign1'],
      // suritySign2: json['suritySign2'],
      // name: json['name'],
      // address: json['address'],
      // balance: json['balance'],
      rejectionDate: json['rejectionDate'],
      rejectionReason: json['rejectionReason'],
      monthlyInstallmentAmt: json['monthlyInstallmentAmt'],
      loanPreClosureReason: json['loanPreClosureReason'],
      pdfString: json['pdfString'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'uid': uid,
      'createdAt': createdAt,
      'totalLoanAmt': totalLoanAmt,
      'appliedLoanAmt': appliedLoanAmt,
      'totalLoanPaid': totalLoanPaid,
      'totalLoanDue': totalLoanDue,
      'loanType': loanType,
      'isSettled': isSettled,
      'settledOn': settledOn, // Nullable
      'isNew': isNew,
      'appliedOn': appliedOn,
      'applicationNo': applicationNo,
      'approvedOn': approvedOn, // Nullable
      'processedOn': processedOn, // Nullable
      'share': share,
      'totalInterestPaid': totalInterestPaid,

      'designation': designation,
      'bAcNo': bAcNo,
      'appliedLoanAmtinWords': appliedLoanAmtinWords,
      'loanReason': loanReason,
      'bSign': bSign,
      'surityName1': surityName1,
      'surityName2': surityName2,
      'sAcNo1': sAcNo1,
      'sAcNo2': sAcNo2,
      // 'suritySign1': suritySign1,
      // 'suritySign2': suritySign2,
      // 'name': name,
      // 'address': address,
      // 'balance': balance,
      'rejectionDate': rejectionDate,
      'rejectionReason': rejectionReason,
      'monthlyInstallmentAmt': monthlyInstallmentAmt,
      'loanPreClosureReason': loanPreClosureReason,
      'pdfString': pdfString,
    };
  }

  // Convert from Firestore snapshot to Loan object
  factory Loan.fromSnap(DocumentSnapshot snap) {
    // final data = snap.data() as Map<String, dynamic>;
    final raw = snap.data();
    if (raw == null) throw Exception('User document missing data');
    final data = Map<String, dynamic>.from(raw as Map);

    return Loan(
      docId: snap.id, // Firestore document ID
      uid: data['uid'] ?? "-",
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      totalLoanAmt: data['totalLoanAmt'] ?? 0,
      appliedLoanAmt: data['appliedLoanAmt'] ?? 0,
      totalLoanPaid: data['totalLoanPaid'] ?? 0,
      totalLoanDue: data['totalLoanDue'] ?? 0,
      loanType: data['loanType'] ?? "-",
      isSettled: data['isSettled'],
      settledOn: data['settledOn'] != null
          ? (data['settledOn'] as Timestamp).toDate()
          : null,
      isNew: data['isNew'],
      appliedOn: (data['appliedOn'] as Timestamp).toDate(),
      applicationNo: data['applicationNo'] ?? 0,
      approvedOn: data['approvedOn'] != null
          ? (data['approvedOn'] as Timestamp).toDate()
          : null,
      processedOn: data['processedOn'] != null
          ? (data['processedOn'] as Timestamp).toDate()
          : null,
      share: data['share'] ?? 0,
      totalInterestPaid: data['totalInterestPaid'] ?? 0,
      designation: data['designation'] ?? "-",
      bAcNo: data['bAcNo'] ?? 0,
      appliedLoanAmtinWords: data['appliedLoanAmtinWords'] ?? "-",
      loanReason: data['loanReason'] ?? "-",
      bSign: data['bSign'] ?? "-",
      surityName1: data['surityName1'] ?? "-",
      surityName2: data['surityName2'] ?? "-",
      sAcNo1: data['sAcNo1'] ?? 0,
      sAcNo2: data['sAcNo2'] ?? 0,
      // suritySign1: data['suritySign1'] ?? "-",
      // suritySign2: data['suritySign2'] ?? "-",
      // name: data['name'],
      // address: data['address'],
      // balance: data['balance'] ?? 0,
      rejectionDate: (data['rejectionDate'] as Timestamp?)?.toDate(),
      rejectionReason: data['rejectionReason'],
      monthlyInstallmentAmt: data['monthlyInstallmentAmt'],
      loanPreClosureReason: data['loanPreClosureReason'],
      pdfString: data['pdfString'],
    );
  }

  // Convert Loan object to Firestore document format
  Map<String, dynamic> toSnap() {
    return {
      'uid': uid,
      'createdAt': Timestamp.fromDate(createdAt),
      'totalLoanAmt': totalLoanAmt,
      'appliedLoanAmt': appliedLoanAmt,
      'totalLoanPaid': totalLoanPaid,
      'totalLoanDue': totalLoanDue,
      'loanType': loanType,
      'isSettled': isSettled,
      'settledOn': settledOn != null ? Timestamp.fromDate(settledOn!) : null,
      'isNew': isNew,
      'appliedOn': Timestamp.fromDate(appliedOn),
      'applicationNo': applicationNo,
      'approvedOn': approvedOn != null ? Timestamp.fromDate(approvedOn!) : null,
      'processedOn':
          processedOn != null ? Timestamp.fromDate(processedOn!) : null,
      'share': share,
      'totalInterestPaid': totalInterestPaid,
      'designation': designation,
      'bAcNo': bAcNo,
      'appliedLoanAmtinWords': appliedLoanAmtinWords,
      'loanReason': loanReason,
      'bSign': bSign,
      'surityName1': surityName1,
      'surityName2': surityName2,
      'sAcNo1': sAcNo1,
      'sAcNo2': sAcNo2,
      // 'suritySign1': suritySign1,
      // 'suritySign2': suritySign2,
      // 'name': name,
      // 'address': address,
      // 'balance': balance ?? 0,
      'rejectionDate': rejectionDate,
      'rejectionReason': rejectionReason,
      'monthlyInstallmentAmt': monthlyInstallmentAmt,
      'loanPreClosureReason': loanPreClosureReason,
      'pdfString': pdfString,
    };
  }

  factory Loan.fromExcelRow(
      List<dynamic> row,
      String uid,
      LoanTypes loantype,
      num appNo,
      num mIAmt,
      num totalLoanAmt,
      num appliedLoanAmt,
      num totalLoanPaid,
      num totalLoanDue,
      num share,
      num totalIntPaid) {
    return Loan(
      docId: '',
      uid: uid,
      createdAt: Timestamp.now().toDate(),
      totalLoanAmt: totalLoanAmt,
      appliedLoanAmt: appliedLoanAmt,
      totalLoanPaid: totalLoanPaid,
      totalLoanDue: totalLoanDue,
      loanType: loantype.toString(),
      isSettled: false,
      settledOn: null,
      isNew: false,
      appliedOn: Timestamp.now().toDate(),
      applicationNo: appNo,
      approvedOn: Timestamp.now().toDate(),
      processedOn: null,
      share: share,
      totalInterestPaid: totalIntPaid,
      designation: null,
      bAcNo: num.tryParse(row[11]?.toString() ?? '0') ?? 0,
      appliedLoanAmtinWords: null,
      loanReason: null,
      bSign: null,
      surityName1: null,
      surityName2: null,
      sAcNo1: null,
      sAcNo2: null,
      // suritySign1: data['suritySign1'] ?? "-",
      // suritySign2: data['suritySign2'] ?? "-",
      // name: data['name'],
      // address: data['address'],
      // balance: data['balance'] ?? 0,
      rejectionDate: null,
      rejectionReason: null,
      monthlyInstallmentAmt: mIAmt,
      loanPreClosureReason: null,
      pdfString: null,
    );
  }
}
