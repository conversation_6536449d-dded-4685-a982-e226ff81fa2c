import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:foodcorp_admin/shared/excel_import.dart';
import 'package:intl/intl.dart';

// 22
class RecoveryMonthlyModel {
  final String docId;
  final int? selectedyear;
  final int? selectedmonth;
  final String doId;
  final num? obLt;
  final num? obSt;
  final num? loanPaidLt;
  final num? loanPaidst;
  final num? loanTotal;
  final num? subs;
  final num? ltInstallment;
  final num? stInstallment;
  final num? interest;
  final num? total;
  final num? installmentRec;
  final DateTime? installmentRecDate;
  final num? ltCb;
  final num? stCb;
  final num? subscriptionPaid;
  final num? longTermInstalmentPaid;
  final num? shortTermInstalmentPaid;
  final num? longTermInterestPaid;
  final num? shortTermInterestPaid;
  final num? totalReceived;
  // final String userId;

  RecoveryMonthlyModel({
    required this.docId,
    required this.obLt,
    required this.obSt,
    required this.loanPaidLt,
    required this.loanPaidst,
    required this.loanTotal,
    required this.subs,
    required this.ltInstallment,
    required this.stInstallment,
    required this.interest,
    required this.total,
    required this.installmentRec,
    this.installmentRecDate,
    required this.ltCb,
    required this.stCb,
    required this.subscriptionPaid,
    required this.longTermInstalmentPaid,
    required this.shortTermInstalmentPaid,
    required this.longTermInterestPaid,
    required this.shortTermInterestPaid,
    required this.totalReceived,
    // required this.userId,
    required this.selectedyear,
    required this.selectedmonth,
    required this.doId,
  });

  // fromJson method to create an instance from a map (JSON)
  factory RecoveryMonthlyModel.fromJson(
      Map<String, dynamic> json, String docId) {
    // var list = json['recoveryData'] as List;
    // List<RecoveryDataModel> recoveryDataList =
    //     list.map((item) => RecoveryDataModel.fromJson(item)).toList();
    // print(json['installmentRecDate'].runtimeType);
    return RecoveryMonthlyModel(
      docId: docId,
      selectedyear: json['selectedyear'],
      selectedmonth: json['selectedmonth'],
      doId: json['doId'],
      obLt: json['obLt'],
      obSt: json['obSt'],
      loanPaidLt: json['loanPaidLt'],
      loanPaidst: json['loanPaidst'],
      loanTotal: json['loanTotal'],
      subs: json['subs'],
      ltInstallment: json['ltInstallment'],
      stInstallment: json['stInstallment'],
      interest: json['interest'],
      total: json['total'],
      installmentRec: json['installmentRec'],
      installmentRecDate: json['installmentRecDate'] is Timestamp
          ? (json['installmentRecDate'] as Timestamp).toDate()
          : json['installmentRecDate'] is String
              ? DateTime.tryParse(json['installmentRecDate'])
              : null,

      ltCb: json['ltCb'],
      stCb: json['stCb'],
      subscriptionPaid: json['subscriptionPaid'],
      longTermInstalmentPaid: json['longTermInstalmentPaid'],
      shortTermInstalmentPaid: json['shortTermInstalmentPaid'],
      longTermInterestPaid: json['longTermInterestPaid'],
      shortTermInterestPaid: json['shortTermInterestPaid'],
      totalReceived: json['totalReceived'],
      // userId: json['userId'],
    );
  }

  // toJson method to convert the instance to a map (JSON)
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'obLt': obLt,
      'obSt': obSt,
      'loanPaidLt': loanPaidLt,
      'loanPaidst': loanPaidst,
      'loanTotal': loanTotal,
      'subs': subs,
      'ltInstallment': ltInstallment,
      'stInstallment': stInstallment,
      'interest': interest,
      'total': total,
      'installmentRec': installmentRec,
      'installmentRecDate': installmentRecDate != null
          ? Timestamp.fromDate(installmentRecDate!)
          : null,

      'ltCb': ltCb,
      'stCb': stCb,
      'subscriptionPaid': subscriptionPaid,
      'longTermInstalmentPaid': longTermInstalmentPaid,
      'shortTermInstalmentPaid': shortTermInstalmentPaid,
      'longTermInterestPaid': longTermInterestPaid,
      'shortTermInterestPaid': shortTermInterestPaid,
      'totalReceived': totalReceived,
      'selectedyear': selectedyear,
      'selectedmonth': selectedmonth,
      'doId': doId,
      // 'userId': userId,
    };
  }

  // fromSnap method to create an instance from a Firestore DocumentSnapshot
  factory RecoveryMonthlyModel.fromSnap(DocumentSnapshot snap) {
    // var data = snap.data() as Map<String, dynamic>;
    final raw = snap.data();
    if (raw == null) throw Exception('User document missing data');
    final data = Map<String, dynamic>.from(raw as Map);

    // var list = data['recoveryData']
    // List<RecoveryDataModel> recoveryDataList =
    //     list.map((item) => RecoveryDataModel.fromJson(item)).toList();

    return RecoveryMonthlyModel(
      docId: snap.id,
      selectedyear: data['selectedyear'],
      selectedmonth: data['selectedmonth'],
      doId: data['doId'],
      obLt: data['obLt'],
      obSt: data['obSt'],
      loanPaidLt: data['loanPaidLt'],
      loanPaidst: data['loanPaidst'],
      loanTotal: data['loanTotal'],
      subs: data['subs'],
      ltInstallment: data['ltInstallment'],
      stInstallment: data['stInstallment'],
      interest: data['interest'],
      total: data['total'],
      installmentRec: data['installmentRec'],
      installmentRecDate: data['installmentRecDate'] == null
          ? null
          : (data['installmentRecDate'] as Timestamp).toDate(),
      ltCb: data['ltCb'],
      stCb: data['stCb'],
      subscriptionPaid: data['subscriptionPaid'],
      longTermInstalmentPaid: data['longTermInstalmentPaid'],
      shortTermInstalmentPaid: data['shortTermInstalmentPaid'],
      longTermInterestPaid: data['longTermInterestPaid'],
      shortTermInterestPaid: data['shortTermInterestPaid'],
      totalReceived: data['totalReceived'],
      // userId: data['userId'],
    );
  }

  // toSnap method to convert the instance to a Firestore Map
  Map<String, dynamic> toSnap() {
    return {
      docId: docId,
      // 'userId': userId,
      'selectedyear': selectedyear,
      'selectedmonth': selectedmonth,
      'doId': doId,
      'obLt': obLt,
      'obSt': obSt,
      'loanPaidLt': loanPaidLt,
      'loanPaidst': loanPaidst,
      'loanTotal': loanTotal,
      'subs': subs,
      'ltInstallment': ltInstallment,
      'stInstallment': stInstallment,
      'interest': interest,
      'total': total,
      'installmentRec': installmentRec,
      'ltCb': ltCb,
      'stCb': stCb,
      'subscriptionPaid': subscriptionPaid,
      'longTermInstalmentPaid': longTermInstalmentPaid,
      'shortTermInstalmentPaid': shortTermInstalmentPaid,
      'longTermInterestPaid': longTermInterestPaid,
      'shortTermInterestPaid': shortTermInterestPaid,
      'totalReceived': totalReceived,
    };
  }

  factory RecoveryMonthlyModel.fromExcelRow(List<dynamic> row) {
    dynamic get(int i) => extractCellValue(i < row.length ? row[i] : null);

    return RecoveryMonthlyModel(
      docId: '',
      selectedyear: DateTime.now().year,
      selectedmonth: DateTime.now().month,
      doId: get(5),
      obLt: num.tryParse(get(23)?.toString() ?? '0') ?? 0,
      obSt: num.tryParse(get(24)?.toString() ?? '0') ?? 0,
      loanPaidLt: num.tryParse(get(25)?.toString() ?? '0') ?? 0,
      loanPaidst: num.tryParse(get(26)?.toString() ?? '0') ?? 0,
      loanTotal: num.tryParse(get(27)?.toString() ?? '0') ?? 0,
      subs: num.tryParse(get(28)?.toString() ?? '0') ?? 0,
      ltInstallment: num.tryParse(get(29)?.toString() ?? '0') ?? 0,
      stInstallment: num.tryParse(get(30)?.toString() ?? '0') ?? 0,
      interest: num.tryParse(get(31)?.toString() ?? '0') ?? 0,
      total: num.tryParse(get(34)?.toString() ?? '0') ?? 0,
      ltCb: num.tryParse(get(35)?.toString() ?? '0') ?? 0,
      stCb: num.tryParse(get(36)?.toString() ?? '0') ?? 0,
      installmentRec: num.tryParse(get(37)?.toString() ?? '0') ?? 0,
      installmentRecDate: () {
        final date = get(38);
        if (date is DateTime) return date;
        if (date is String && date.trim().isNotEmpty) {
          try {
            return DateFormat('dd.MM.yyyy').parse(date);
          } catch (e) {
            print("⚠️ Invalid recovery date format: '$date'");
          }
        }
        return null;
      }(),
      subscriptionPaid: null,
      longTermInstalmentPaid: null,
      shortTermInstalmentPaid: null,
      longTermInterestPaid: null,
      shortTermInterestPaid: null,
      totalReceived: null,
    );
  }
}

// class RecoveryDataModel {
//   final num cpfNo;
//   final String name;
//   final String districtoffice;
//   final num? obLt;
//   final num? obSt;
//   final num? loanPaidLt;
//   final num? loanPaidst;
//   final num? loanTotal;
//   final num? subs;
//   final num? ltInstallment;
//   final num? stInstallment;
//   final num? interest;
//   final num? total;
//   final num? installmentRec;
//   final num? ltCb;
//   final num? stCb;
//   final num subscriptionPaid;
//   final num longTermInstalmentPaid;
//   final num shortTermInstalmentPaid;
//   final num longTermInterestPaid;
//   final num shortTermInterestPaid;
//   final num totalReceived;

//   RecoveryDataModel({
//     required this.cpfNo,
//     required this.name,
//     required this.districtoffice,
//     required this.obLt,
//     required this.obSt,
//     required this.loanPaidLt,
//     required this.loanPaidst,
//     required this.loanTotal,
//     required this.subs,
//     required this.ltInstallment,
//     required this.stInstallment,
//     required this.interest,
//     required this.total,
//     required this.installmentRec,
//     required this.ltCb,
//     required this.stCb,
//     required this.subscriptionPaid,
//     required this.longTermInstalmentPaid,
//     required this.shortTermInstalmentPaid,
//     required this.longTermInterestPaid,
//     required this.shortTermInterestPaid,
//     required this.totalReceived,
//   });

//   // fromJson method to create an instance from a map (JSON)
//   factory RecoveryDataModel.fromJson(Map<String, dynamic> json) {
//     return RecoveryDataModel(
//       cpfNo: json['cpfNo'],
//       name: json['name'],
//       districtoffice: json['districtoffice'],
//       obLt: json['obLt'],
//       obSt: json['obSt'],
//       loanPaidLt: json['loanPaidLt'],
//       loanPaidst: json['loanPaidst'],
//       loanTotal: json['loanTotal'],
//       subs: json['subs'],
//       ltInstallment: json['ltInstallment'],
//       stInstallment: json['stInstallment'],
//       interest: json['interest'],
//       total: json['total'],
//       installmentRec: json['installmentRec'],
//       ltCb: json['ltCb'],
//       stCb: json['stCb'],
//       subscriptionPaid: json['subscriptionPaid'],
//       longTermInstalmentPaid: json['longTermInstalmentPaid'],
//       shortTermInstalmentPaid: json['shortTermInstalmentPaid'],
//       longTermInterestPaid: json['longTermInterestPaid'],
//       shortTermInterestPaid: json['shortTermInterestPaid'],
//       totalReceived: json['totalReceived'],
//     );
//   }

//   // toJson method to convert the instance to a map (JSON)
//   Map<String, dynamic> toJson() {
//     return {
//       'cpfNo': cpfNo,
//       'name': name,
//       'districtoffice': districtoffice,
//       'obLt': obLt,
//       'obSt': obSt,
//       'loanPaidLt': loanPaidLt,
//       'loanPaidst': loanPaidst,
//       'loanTotal': loanTotal,
//       'subs': subs,
//       'ltInstallment': ltInstallment,
//       'stInstallment': stInstallment,
//       'interest': interest,
//       'total': total,
//       'installmentRec': installmentRec,
//       'ltCb': ltCb,
//       'stCb': stCb,
//       'subscriptionPaid': subscriptionPaid,
//       'longTermInstalmentPaid': longTermInstalmentPaid,
//       'shortTermInstalmentPaid': shortTermInstalmentPaid,
//       'longTermInterestPaid': longTermInterestPaid,
//       'shortTermInterestPaid': shortTermInterestPaid,
//       'totalReceived': totalReceived,
//     };
//   }

//   // fromSnap method to create an instance from a Firestore DocumentSnapshot
//   factory RecoveryDataModel.fromSnap(DocumentSnapshot snap) {
//     var data = snap.data() as Map<String, dynamic>;
//     return RecoveryDataModel(
//       cpfNo: data['cpfNo'],
//       name: data['name'],
//       districtoffice: data['districtoffice'],
//       obLt: data['obLt'],
//       obSt: data['obSt'],
//       loanPaidLt: data['loanPaidLt'],
//       loanPaidst: data['loanPaidst'],
//       loanTotal: data['loanTotal'],
//       subs: data['subs'],
//       ltInstallment: data['ltInstallment'],
//       stInstallment: data['stInstallment'],
//       interest: data['interest'],
//       total: data['total'],
//       installmentRec: data['installmentRec'],
//       ltCb: data['ltCb'],
//       stCb: data['stCb'],
//       subscriptionPaid: data['subscriptionPaid'],
//       longTermInstalmentPaid: data['longTermInstalmentPaid'],
//       shortTermInstalmentPaid: data['shortTermInstalmentPaid'],
//       longTermInterestPaid: data['longTermInterestPaid'],
//       shortTermInterestPaid: data['shortTermInterestPaid'],
//       totalReceived: data['totalReceived'],
//     );
//   }

//   // toSnap method to convert the instance to a Firestore Map
//   Map<String, dynamic> toSnap() {
//     return {
//       'cpfNo': cpfNo,
//       'name': name,
//       'districtoffice': districtoffice,
//       'obLt': obLt,
//       'obSt': obSt,
//       'loanPaidLt': loanPaidLt,
//       'loanPaidst': loanPaidst,
//       'loanTotal': loanTotal,
//       'subs': subs,
//       'ltInstallment': ltInstallment,
//       'stInstallment': stInstallment,
//       'interest': interest,
//       'total': total,
//       'installmentRec': installmentRec,
//       'ltCb': ltCb,
//       'stCb': stCb,
//       'subscriptionPaid': subscriptionPaid,
//       'longTermInstalmentPaid': longTermInstalmentPaid,
//       'shortTermInstalmentPaid': shortTermInstalmentPaid,
//       'longTermInterestPaid': longTermInterestPaid,
//       'shortTermInterestPaid': shortTermInterestPaid,
//       'totalReceived': totalReceived,
//     };
//   }

//   Map<String, dynamic> toMap() {
//     return {
//       'cpfNo': cpfNo,
//       'name': name,
//       'districtoffice': districtoffice,
//       'obLt': obLt,
//       'obSt': obSt,
//       'loanPaidLt': loanPaidLt,
//       'loanPaidst': loanPaidst,
//       'loanTotal': loanTotal,
//       'subs': subs,
//       'ltInstallment': ltInstallment,
//       'stInstallment': stInstallment,
//       'interest': interest,
//       'total': total,
//       'installmentRec': installmentRec,
//       'ltCb': ltCb,
//       'stCb': stCb,
//       'subscriptionPaid': subscriptionPaid,
//       'longTermInstalmentPaid': longTermInstalmentPaid,
//       'shortTermInstalmentPaid': shortTermInstalmentPaid,
//       'longTermInterestPaid': longTermInterestPaid,
//       'shortTermInterestPaid': shortTermInterestPaid,
//       'totalReceived': totalReceived,
//     };
//   }
// }
