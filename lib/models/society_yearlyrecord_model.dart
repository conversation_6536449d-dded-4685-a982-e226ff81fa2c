import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/excel_import.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';

class SocietyYearlyRecordModel {
  final String docId;
  // final String doId;
  final int selectedyear;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final num openingBalance; // OB
  final num closingBalance; // CB
  final num totalSubscription;
  final num intOnSubscription; // Interest on subscription
  final num subscriptionInterestRate; // Interest rate on subscription
  final num totalLoanGiven;
  final num totalLoanReceived;
  final num ltLoanReceived; // Long-term loans received
  final num stLoanReceived; // Short-term loans received
  final num ltLoanGiven; // Long-term loans given
  final num stLoanGiven; // Short-term loans given
  final num ltIntAmt; // Long-term interest amount
  final num stIntAmt; // Short-term interest amount
  final num totalIntAmt; // Total interest amount
  final num loanIntRate; // Loan interest rate
  final num totalPendingLoan; // Total pending loans
  final num ltPendingLoan; // Long-term pending loans
  final num stPendingLoan; // Short-term pending loans
  final num totalExpenses; // Total expenses for the year
  final List<String>
      expensesIds; // IDs of expenses (assuming a list of strings)
  final num totalDividend; // Total dividend
  final num totalMonthlyShareGiven; // Total monthly shares given
  final num totalShareGiven; // Total shares given
  final num monthlyDividend; // Monthly dividend
  final num dividendRate; // Dividend rate
  final num cashBalance;

  SocietyYearlyRecordModel({
    required this.docId,
    required this.selectedyear,
    // required this.doId,
    required this.createdAt,
    required this.updatedAt,
    required this.openingBalance,
    required this.closingBalance,
    required this.totalSubscription,
    required this.intOnSubscription,
    required this.subscriptionInterestRate,
    required this.totalLoanGiven,
    required this.totalLoanReceived,
    required this.ltLoanReceived,
    required this.stLoanReceived,
    required this.ltLoanGiven,
    required this.stLoanGiven,
    required this.ltIntAmt,
    required this.stIntAmt,
    required this.totalIntAmt,
    required this.loanIntRate,
    required this.totalPendingLoan,
    required this.ltPendingLoan,
    required this.stPendingLoan,
    required this.totalExpenses,
    required this.expensesIds,
    required this.totalDividend,
    required this.totalMonthlyShareGiven,
    required this.totalShareGiven,
    required this.monthlyDividend,
    required this.dividendRate,
    required this.cashBalance,
  });

  factory SocietyYearlyRecordModel.fromJson(Map<String, dynamic> json) {
    return SocietyYearlyRecordModel(
      docId: json['docId'],
      // doId: json['doId'],
      selectedyear: json['selectedyear'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      openingBalance: json['OB'], // Opening Balance
      closingBalance: json['CB'], // Closing Balance
      totalSubscription: json['totalSubscription'],
      intOnSubscription: json['intOnSubscription'],
      subscriptionInterestRate: json['subscriptionInterestRate'],
      totalLoanGiven: json['totalLoanGiven'],
      totalLoanReceived: json['totalLoanReceived'],
      ltLoanReceived: json['ltLoanReceived'],
      stLoanReceived: json['stLoanReceived'],
      ltLoanGiven: json['ltLoanGiven'],
      stLoanGiven: json['stLoanGiven'],
      ltIntAmt: json['ltIntAmt'],
      stIntAmt: json['stIntAmt'],
      totalIntAmt: json['totalIntAmt'],
      loanIntRate: json['loanIntRate'],
      totalPendingLoan: json['totalPendingLoan'],
      ltPendingLoan: json['ltPendingLoan'],
      stPendingLoan: json['stPendingLoan'],
      totalExpenses: json['totalExpenses'],
      expensesIds:
          List<String>.from(json['expensesIds']), // Assuming a list of strings
      totalDividend: json['totalDividend'],
      totalMonthlyShareGiven: json['totalMonthlyShareGiven'],
      totalShareGiven: json['totalShareGiven'],
      monthlyDividend: json['monthlyDividend'],
      dividendRate: json['dividendRate'],
      cashBalance: json['cashBalance'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      // 'doId': doId,
      'selectedyear': selectedyear,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'OB': openingBalance, // Opening Balance
      'CB': closingBalance, // Closing Balance
      'totalSubscription': totalSubscription,
      'intOnSubscription': intOnSubscription,
      'subscriptionInterestRate': subscriptionInterestRate,
      'totalLoanGiven': totalLoanGiven,
      'totalLoanReceived': totalLoanReceived,
      'ltLoanReceived': ltLoanReceived,
      'stLoanReceived': stLoanReceived,
      'ltLoanGiven': ltLoanGiven,
      'stLoanGiven': stLoanGiven,
      'ltIntAmt': ltIntAmt,
      'stIntAmt': stIntAmt,
      'totalIntAmt': totalIntAmt,
      'loanIntRate': loanIntRate,
      'totalPendingLoan': totalPendingLoan,
      'ltPendingLoan': ltPendingLoan,
      'stPendingLoan': stPendingLoan,
      'totalExpenses': totalExpenses,
      'expensesIds': expensesIds,
      'totalDividend': totalDividend,
      'totalMonthlyShareGiven': totalMonthlyShareGiven,
      'totalShareGiven': totalShareGiven,
      'monthlyDividend': monthlyDividend,
      'dividendRate': dividendRate,
      'cashBalance': cashBalance,
    };
  }

  // Convert from Firestore snapshot to SocietyYearlyRecordModel object
  factory SocietyYearlyRecordModel.fromSnap(DocumentSnapshot snap) {
    // final data = snap.data() as Map<String, dynamic>;
    final raw = snap.data();
    if (raw == null) throw Exception('User document missing data');
    final data = Map<String, dynamic>.from(raw as Map);

    return SocietyYearlyRecordModel(
      docId: snap.id,
      // doId: data['doId'],
      selectedyear: data['selectedyear'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      openingBalance: data['OB'], // Opening Balance
      closingBalance: data['CB'], // Closing Balance
      totalSubscription: data['totalSubscription'],
      intOnSubscription: data['intOnSubscription'],
      subscriptionInterestRate: data['subscriptionInterestRate'],
      totalLoanGiven: data['totalLoanGiven'],
      totalLoanReceived: data['totalLoanReceived'],
      ltLoanReceived: data['ltLoanReceived'],
      stLoanReceived: data['stLoanReceived'],
      ltLoanGiven: data['ltLoanGiven'],
      stLoanGiven: data['stLoanGiven'],
      ltIntAmt: data['ltIntAmt'],
      stIntAmt: data['stIntAmt'],
      totalIntAmt: data['totalIntAmt'],
      loanIntRate: data['loanIntRate'],
      totalPendingLoan: data['totalPendingLoan'],
      ltPendingLoan: data['ltPendingLoan'],
      stPendingLoan: data['stPendingLoan'],
      totalExpenses: data['totalExpenses'],
      expensesIds: List<String>.from(data['expensesIds']),
      totalDividend: data['totalDividend'],
      totalMonthlyShareGiven: data['totalMonthlyShareGiven'],
      totalShareGiven: data['totalShareGiven'],
      monthlyDividend: data['monthlyDividend'],
      dividendRate: data['dividendRate'],
      cashBalance: data['cashBalance'],
    );
  }

  // Convert SocietyYearlyRecordModel object to Firestore document format
  Map<String, dynamic> toSnap() {
    return {
      // 'doId': doId,
      'selectedyear': selectedyear,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'OB': openingBalance, // Opening Balance
      'CB': closingBalance, // Closing Balance
      'totalSubscription': totalSubscription,
      'intOnSubscription': intOnSubscription,
      'subscriptionInterestRate': subscriptionInterestRate,
      'totalLoanGiven': totalLoanGiven,
      'totalLoanReceived': totalLoanReceived,
      'ltLoanReceived': ltLoanReceived,
      'stLoanReceived': stLoanReceived,
      'ltLoanGiven': ltLoanGiven,
      'stLoanGiven': stLoanGiven,
      'ltIntAmt': ltIntAmt,
      'stIntAmt': stIntAmt,
      'totalIntAmt': totalIntAmt,
      'loanIntRate': loanIntRate,
      'totalPendingLoan': totalPendingLoan,
      'ltPendingLoan': ltPendingLoan,
      'stPendingLoan': stPendingLoan,
      'totalExpenses': totalExpenses,
      'expensesIds': expensesIds,
      'totalDividend': totalDividend,
      'totalMonthlyShareGiven': totalMonthlyShareGiven,
      'totalShareGiven': totalShareGiven,
      'monthlyDividend': monthlyDividend,
      'dividendRate': dividendRate,
      'cashBalance': cashBalance,
    };
  }

  factory SocietyYearlyRecordModel.fromExcelRow(List<dynamic> row) {
    dynamic get(int i) => extractCellValue(i < row.length ? row[i] : null);

    final subs = num.tryParse(get(21)?.toString() ?? '0') ?? 0;
    final shares = num.tryParse(get(22)?.toString() ?? '0') ?? 0;
    final ltLoan = num.tryParse(get(17)?.toString() ?? '0') ?? 0;
    final stLoan = num.tryParse(get(13)?.toString() ?? '0') ?? 0;
    final ltPaid = num.tryParse(get(18)?.toString() ?? '0') ?? 0;
    final stPaid = num.tryParse(get(14)?.toString() ?? '0') ?? 0;
    final ltInt = num.tryParse(get(20)?.toString() ?? '0') ?? 0;
    final stInt = num.tryParse(get(16)?.toString() ?? '0') ?? 0;
    final ltPending = num.tryParse(get(19)?.toString() ?? '0') ?? 0;
    final stPending = num.tryParse(get(15)?.toString() ?? '0') ?? 0;

    final subIntRate = num.tryParse(
            Get.find<HomeCtrl>().settings?.subscriptionInterest ?? "0") ??
        0;
    final divRate =
        num.tryParse(Get.find<HomeCtrl>().settings?.dividentRate ?? "0") ?? 0;

    return SocietyYearlyRecordModel(
      docId: '',
      // doId: get(5),
      selectedyear: TheFinancialYear.getCurrentYearForDatabase(),
      createdAt: Timestamp.now().toDate(),
      updatedAt: null,
      openingBalance: (num.tryParse(get(23)?.toString() ?? '0') ?? 0) +
          (num.tryParse(get(24)?.toString() ?? '0') ?? 0),
      closingBalance: (num.tryParse(get(35)?.toString() ?? '0') ?? 0) +
          (num.tryParse(get(36)?.toString() ?? '0') ?? 0),
      totalSubscription: subs,
      intOnSubscription: (subs * subIntRate) / 100,
      subscriptionInterestRate: subIntRate,
      totalLoanGiven: ltLoan + stLoan,
      ltLoanGiven: ltLoan,
      stLoanGiven: stLoan,
      totalLoanReceived: stPaid + ltPaid,
      ltLoanReceived: ltPaid,
      stLoanReceived: stPaid,
      ltIntAmt: ltInt,
      stIntAmt: stInt,
      totalIntAmt: ltInt + stInt,
      loanIntRate: 0,
      totalPendingLoan: ltInt + stInt,
      ltPendingLoan: ltPending,
      stPendingLoan: stPending,
      totalExpenses: 0,
      expensesIds: [],
      totalDividend: (shares * divRate) / 100,
      totalMonthlyShareGiven: 0,
      totalShareGiven: shares,
      monthlyDividend: 0,
      dividendRate: divRate,
      cashBalance: 0,
    );
  }

  SocietyYearlyRecordModel mergeWith(SocietyYearlyRecordModel other) {
    return SocietyYearlyRecordModel(
      docId: docId,
      selectedyear: selectedyear,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      openingBalance: openingBalance + other.openingBalance,
      closingBalance: closingBalance + other.closingBalance,
      totalSubscription: totalSubscription + other.totalSubscription,
      intOnSubscription: intOnSubscription + other.intOnSubscription,
      subscriptionInterestRate: subscriptionInterestRate,
      totalLoanGiven: totalLoanGiven + other.totalLoanGiven,
      totalLoanReceived: totalLoanReceived + other.totalLoanReceived,
      ltLoanReceived: ltLoanReceived + other.ltLoanReceived,
      stLoanReceived: stLoanReceived + other.stLoanReceived,
      ltLoanGiven: ltLoanGiven + other.ltLoanGiven,
      stLoanGiven: stLoanGiven + other.stLoanGiven,
      ltIntAmt: ltIntAmt + other.ltIntAmt,
      stIntAmt: stIntAmt + other.stIntAmt,
      totalIntAmt: totalIntAmt + other.totalIntAmt,
      loanIntRate: loanIntRate,
      totalPendingLoan: totalPendingLoan + other.totalPendingLoan,
      ltPendingLoan: ltPendingLoan + other.ltPendingLoan,
      stPendingLoan: stPendingLoan + other.stPendingLoan,
      totalExpenses: totalExpenses + other.totalExpenses,
      expensesIds: [...expensesIds, ...other.expensesIds],
      totalDividend: totalDividend + other.totalDividend,
      totalMonthlyShareGiven:
          totalMonthlyShareGiven + other.totalMonthlyShareGiven,
      totalShareGiven: totalShareGiven + other.totalShareGiven,
      monthlyDividend: monthlyDividend + other.monthlyDividend,
      dividendRate: dividendRate,
      cashBalance: cashBalance + other.cashBalance,
    );
  }
}
