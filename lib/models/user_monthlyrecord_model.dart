import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/excel_import.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

//26
class UserMonthlyRecordModel {
  final String docId;
  final int? selectedyear;
  final int? selectedmonth;
  final num cpfNo;
  final String name;
  final String districtoffice;
  final num? obLt;
  final num? obSt;
  final num? obSubs;
  final num? loanPaidLt;
  final num? loanPaidst;
  final num? loanTotal;
  final num? subs;
  final num? ltInstallment;
  final num? stInstallment;
  final num? interest;
  final num? total;
  final num? installmentRec;
  final DateTime? installmentRecDate;
  final num? ltCb;
  final num? stCb;
  final num subscriptionPaid;
  final num longTermInstalmentPaid;
  final num shortTermInstalmentPaid;
  final num longTermInterestPaid;
  final num shortTermInterestPaid;
  final num penaltyPaid;
  final bool isPaid;
  final String? status;
  final num? dues;
  final num? penalty;
  final num shareValue;
  final num? societySubsPayout;
  final num? societySharesPayout;

  UserMonthlyRecordModel({
    required this.societySubsPayout,
    required this.societySharesPayout,
    required this.shareValue,
    required this.dues,
    required this.penalty,
    required this.docId,
    this.status,
    this.obSubs,
    this.selectedyear,
    this.selectedmonth,
    required this.cpfNo,
    required this.name,
    required this.districtoffice,
    this.obLt,
    this.obSt,
    this.loanPaidLt,
    this.loanPaidst,
    this.loanTotal,
    this.subs,
    this.ltInstallment,
    this.stInstallment,
    this.interest,
    this.total,
    this.installmentRec,
    this.installmentRecDate,
    this.ltCb,
    this.stCb,
    required this.subscriptionPaid,
    required this.longTermInstalmentPaid,
    required this.shortTermInstalmentPaid,
    required this.longTermInterestPaid,
    required this.shortTermInterestPaid,
    required this.penaltyPaid,
    required this.isPaid,
  });

  factory UserMonthlyRecordModel.fromJson(
      Map<String, dynamic> json, String docId) {
    return UserMonthlyRecordModel(
      docId: docId,
      selectedyear: json['selectedyear'],
      selectedmonth: json['selectedmonth'],
      cpfNo: json['cpfNo'],
      name: json['name'],
      districtoffice: json['districtoffice'],
      obLt: json['obLt'],
      obSt: json['obSt'],
      obSubs: json['obSubs'],
      loanPaidLt: json['loanPaidLt'],
      loanPaidst: json['loanPaidst'],
      loanTotal: json['loanTotal'],
      subs: json['subs'],
      ltInstallment: json['ltInstallment'],
      stInstallment: json['stInstallment'],
      interest: json['interest'],
      total: json['total'],
      installmentRec: json['installmentRec'],
      installmentRecDate: json['installmentRecDate'] == null
          ? null
          : (json['installmentRecDate'] as Timestamp).toDate(),
      ltCb: json['ltCb'],
      stCb: json['stCb'],
      subscriptionPaid: json['subscriptionPaid'],
      longTermInstalmentPaid: json['longTermInstalmentPaid'],
      shortTermInstalmentPaid: json['shortTermInstalmentPaid'],
      longTermInterestPaid: json['longTermInterestPaid'],
      shortTermInterestPaid: json['shortTermInterestPaid'],
      isPaid: json['isPaid'],
      status: json['status'],
      dues: json['dues'],
      penalty: json['penalty'],
      penaltyPaid: json['penaltyPaid'],
      shareValue: json['shareValue'],
      societySubsPayout: json['societySubsPayout'],
      societySharesPayout: json['societySharesPayout'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'selectedyear': selectedyear,
      'selectedmonth': selectedmonth,
      'cpfNo': cpfNo,
      'name': name,
      'districtoffice': districtoffice,
      'obLt': obLt,
      'obSt': obSt,
      'obSubs': obSubs,
      'loanPaidLt': loanPaidLt,
      'loanPaidst': loanPaidst,
      'loanTotal': loanTotal,
      'subs': subs,
      'ltInstallment': ltInstallment,
      'stInstallment': stInstallment,
      'interest': interest,
      'total': total,
      'installmentRec': installmentRec,
      'installmentRecDate': installmentRecDate,
      'ltCb': ltCb,
      'stCb': stCb,
      'subscriptionPaid': subscriptionPaid,
      'longTermInstalmentPaid': longTermInstalmentPaid,
      'shortTermInstalmentPaid': shortTermInstalmentPaid,
      'longTermInterestPaid': longTermInterestPaid,
      'shortTermInterestPaid': shortTermInterestPaid,
      'isPaid': isPaid,
      'status': status,
      'dues': dues,
      'penalty': penalty,
      'penaltyPaid': penaltyPaid,
      'shareValue': shareValue,
      'societySubsPayout': societySubsPayout,
      'societySharesPayout': societySharesPayout,
    };
  }

  factory UserMonthlyRecordModel.fromSnap(DocumentSnapshot snap) {
    // final data = snap.data() as Map<String, dynamic>;
    final raw = snap.data();
    if (raw == null) throw Exception('User document missing data');
    final data = Map<String, dynamic>.from(raw as Map);

    return UserMonthlyRecordModel(
      docId: snap.id,
      selectedyear: data['selectedyear'],
      selectedmonth: data['selectedmonth'],
      // doId: data['doId'],
      cpfNo: data['cpfNo'],
      name: data['name'] ?? '',
      districtoffice: data['districtoffice'] ?? '',
      obLt: data['obLt'] ?? 0,
      obSt: data['obSt'] ?? 0,
      obSubs: data['obSubs'] ?? 0,
      loanPaidLt: data['loanPaidLt'] ?? 0,
      loanPaidst: data['loanPaidst'] ?? 0,
      loanTotal: data['loanTotal'] ?? 0,
      subs: data['subs'] ?? 0,
      ltInstallment: data['ltInstallment'] ?? 0,
      stInstallment: data['stInstallment'] ?? 0,
      interest: data['interest'] ?? 0,
      total: data['total'] ?? 0,
      installmentRec: data['installmentRec'] ?? 0,
      installmentRecDate: data['installmentRecDate'] == null
          ? null
          : (data['installmentRecDate'] as Timestamp).toDate(),
      ltCb: data['ltCb'] ?? 0,
      stCb: data['stCb'] ?? 0,
      subscriptionPaid: data['subscriptionPaid'] ?? 0,
      longTermInstalmentPaid: data['longTermInstalmentPaid'] ?? 0,
      shortTermInstalmentPaid: data['shortTermInstalmentPaid'] ?? 0,
      longTermInterestPaid: data['longTermInterestPaid'] ?? 0,
      shortTermInterestPaid: data['shortTermInterestPaid'] ?? 0,
      isPaid: data['isPaid'],
      status: data['status'],
      dues: data['dues'] ?? 0,
      penalty: data['penalty'] ?? 0,
      penaltyPaid: data['penaltyPaid'] ?? 0,
      shareValue: data['shareValue'] ?? 0,
      societySubsPayout: data['societySubsPayout'],
      societySharesPayout: data['societySharesPayout'],
    );
  }

  factory UserMonthlyRecordModel.fromExcelRow(List<dynamic> row, int cpfNo) {
    dynamic get(int i) => extractCellValue(i < row.length ? row[i] : null);

    return UserMonthlyRecordModel(
      docId: '',
      selectedyear: DateTime.now().year,
      selectedmonth: DateTime.now().month,
      cpfNo: cpfNo,
      name: get(2),
      districtoffice: get(6),
      obLt: num.tryParse(get(23)?.toString() ?? '0'),
      obSt: num.tryParse(get(24)?.toString() ?? '0'),
      obSubs: num.tryParse(get(21)?.toString() ?? '0'),
      loanPaidLt: num.tryParse(get(25)?.toString() ?? '0'),
      loanPaidst: num.tryParse(get(26)?.toString() ?? '0'),
      loanTotal: num.tryParse(get(27)?.toString() ?? '0'),
      subs: num.tryParse(get(28)?.toString() ?? '0'),
      ltInstallment: num.tryParse(get(29)?.toString() ?? '0'),
      stInstallment: num.tryParse(get(30)?.toString() ?? '0'),
      interest: num.tryParse(get(31)?.toString() ?? '0'),
      total: num.tryParse(get(32)?.toString() ?? '0'),
      installmentRec: num.tryParse(get(37)?.toString() ?? '0'),
      installmentRecDate: () {
        final val = get(38);
        if (val is DateTime) return val;
        if (val is String && val.trim().isNotEmpty) {
          try {
            return DateFormat('dd.MM.yyyy').parse(val);
          } catch (_) {
            print("⚠️ Invalid date: $val for CPF: $cpfNo");
          }
        }
        return null;
      }(),
      ltCb: num.tryParse(get(35)?.toString() ?? '0'),
      stCb: num.tryParse(get(36)?.toString() ?? '0'),
      subscriptionPaid: num.tryParse(get(28)?.toString() ?? '0') ?? 0,
      longTermInstalmentPaid: num.tryParse(get(29)?.toString() ?? '0') ?? 0,
      shortTermInstalmentPaid: num.tryParse(get(30)?.toString() ?? '0') ?? 0,
      longTermInterestPaid: (() {
        final obLt = num.tryParse(get(23)?.toString() ?? '0') ?? 0;
        final loanPaidLt = num.tryParse(get(25)?.toString() ?? '0') ?? 0;
        final interestRate = num.tryParse(
                Get.find<HomeCtrl>().settings?.ltloanInterest.toString() ??
                    '0') ??
            0;
        return (obLt != 0) ? (obLt + loanPaidLt * interestRate) / 1200 : 0;
      })(),
      shortTermInterestPaid: (() {
        final obSt = num.tryParse(get(24)?.toString() ?? '0') ?? 0;
        final loanPaidSt = num.tryParse(get(26)?.toString() ?? '0') ?? 0;
        final interestRate = num.tryParse(
                Get.find<HomeCtrl>().settings?.stloanInterest.toString() ??
                    '0') ??
            0;
        return (obSt != 0) ? (obSt + loanPaidSt * interestRate) / 1200 : 0;
      })(),
      penaltyPaid: num.tryParse(get(44)?.toString() ?? '0') ?? 0,
      isPaid: true,
      status: 'paid',
      dues: num.tryParse(get(32)?.toString() ?? '0') ?? 0,
      penalty: num.tryParse(get(33)?.toString() ?? '0') ?? 0,
      shareValue: num.tryParse(get(41)?.toString() ?? '0') ?? 0,
      // num.tryParse(get(22)?.toString() ?? '0') ?? 0,
      societySubsPayout: null,
      societySharesPayout: null,
    );
  }
}
