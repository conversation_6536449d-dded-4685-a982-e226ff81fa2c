const bool testMode = false;

class LoanTypes {
  static const longTerm = 'Long Term Loan';
  static const emergencyLoan = 'Emergency Loan';
}

List<String> month = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec'
];

/// Financial Year Utility Class
/// Handles all financial year related operations for the project
/// Financial Year: April 1st to March 31st
class TheFinancialYear {
  /// Get the current financial year string (e.g., "2023-24")
  static String getCurrentFinancialYear() {
    final now = DateTime.now();
    final int startYear = now.month >= 4 ? now.year : now.year - 1;
    return "$startYear-${(startYear + 1).toString().substring(2)}";
  }

  /// Get the current financial year start year (e.g., 2023 for FY 2023-24)
  static int getCurrentFinancialYearStartYear() {
    final now = DateTime.now();
    return now.month >= 4 ? now.year : now.year - 1;
  }

  /// Convert financial year string to start year (e.g., "2023-24" -> 2023)
  static int financialYearToStartYear(String financialYear) {
    return int.parse(financialYear.split('-')[0]);
  }

  /// Convert start year to financial year string (e.g., 2023 -> "2023-24")
  static String startYearToFinancialYear(int startYear) {
    return "$startYear-${(startYear + 1).toString().substring(2)}";
  }

  /// Get financial year for a specific date
  static String getFinancialYearForDate(DateTime date) {
    final int startYear = date.month >= 4 ? date.year : date.year - 1;
    return "$startYear-${(startYear + 1).toString().substring(2)}";
  }

  /// Get start date of a financial year (April 1st)
  static DateTime getFinancialYearStartDate(int startYear) {
    return DateTime(startYear, 4, 1);
  }

  /// Get end date of a financial year (March 31st)
  static DateTime getFinancialYearEndDate(int startYear) {
    return DateTime(startYear + 1, 3, 31, 23, 59, 59, 999);
  }

  /// Generate list of financial year strings for dropdowns
  /// Returns current FY with previous and next FY (3 years total)
  static List<String> generateFinancialYearsList({
    int count = 3,
    bool includeCurrentAndFuture = true,
  }) {
    final currentFYStartYear = getCurrentFinancialYearStartYear();
    final List<String> years = [];

    // Always show: Previous FY, Current FY, Next FY
    years.add(startYearToFinancialYear(currentFYStartYear - 1)); // Previous
    years.add(startYearToFinancialYear(currentFYStartYear)); // Current
    years.add(startYearToFinancialYear(currentFYStartYear + 1)); // Next

    return years;
  }

  /// Generate list of financial year start years for database queries
  /// Returns current FY with previous and next FY start years (3 years total)
  static List<int> generateFinancialYearStartYearsList({
    int count = 3,
    bool includeCurrentAndFuture = true,
  }) {
    final currentFYStartYear = getCurrentFinancialYearStartYear();
    final List<int> years = [];

    // Always show: Previous FY, Current FY, Next FY
    years.add(currentFYStartYear - 1); // Previous
    years.add(currentFYStartYear); // Current
    years.add(currentFYStartYear + 1); // Next

    return years;
  }

  /// Check if a date falls within a specific financial year
  static bool isDateInFinancialYear(DateTime date, int financialYearStartYear) {
    final fyStart = getFinancialYearStartDate(financialYearStartYear);
    final fyEnd = getFinancialYearEndDate(financialYearStartYear);
    return (date.isAfter(fyStart) || date.isAtSameMomentAs(fyStart)) &&
        (date.isBefore(fyEnd) || date.isAtSameMomentAs(fyEnd));
  }

  /// Get the financial year start year for database storage
  /// This maintains compatibility with existing integer year fields
  static int getFinancialYearForStorage() {
    return getCurrentFinancialYearStartYear();
  }

  /// Alias for getFinancialYearForStorage for clarity
  static int getCurrentYearForDatabase() {
    return getCurrentFinancialYearStartYear();
  }

  /// Check if current date is in transition period (Jan-Mar)
  static bool isInTransitionPeriod() {
    final now = DateTime.now();
    return now.month >= 1 && now.month <= 3;
  }

  /// Get financial year months in order (April=1, May=2, ..., March=12)
  /// Returns list of calendar month numbers in financial year sequence
  static List<int> getFinancialYearMonths() {
    return [4, 5, 6, 7, 8, 9, 10, 11, 12, 1, 2, 3];
  }

  /// Convert calendar month (1-12) to financial year month position (1-12)
  /// April = 1, May = 2, ..., March = 12
  static int convertCalendarToFYMonth(int calendarMonth) {
    if (calendarMonth < 1 || calendarMonth > 12) {
      throw ArgumentError('Calendar month must be between 1 and 12');
    }
    // April (4) becomes 1, May (5) becomes 2, ..., March (3) becomes 12
    return calendarMonth >= 4 ? calendarMonth - 3 : calendarMonth + 9;
  }

  /// Convert financial year month position (1-12) to calendar month (1-12)
  /// FY Month 1 = April (4), FY Month 2 = May (5), ..., FY Month 12 = March (3)
  static int convertFYToCalendarMonth(int fyMonth) {
    if (fyMonth < 1 || fyMonth > 12) {
      throw ArgumentError('Financial year month must be between 1 and 12');
    }
    // FY 1 becomes April (4), FY 2 becomes May (5), ..., FY 12 becomes March (3)
    return fyMonth <= 9 ? fyMonth + 3 : fyMonth - 9;
  }

  /// Get month name for financial year month position
  /// fyMonth: 1=April, 2=May, ..., 12=March
  static String getFinancialYearMonthName(int fyMonth, {bool short = false}) {
    final calendarMonth = convertFYToCalendarMonth(fyMonth);
    const longNames = [
      '',
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    const shortNames = [
      '',
      'JAN',
      'FEB',
      'MAR',
      'APR',
      'MAY',
      'JUN',
      'JUL',
      'AUG',
      'SEP',
      'OCT',
      'NOV',
      'DEC'
    ];
    return short ? shortNames[calendarMonth] : longNames[calendarMonth];
  }

  /// Get financial year month position for a calendar month
  /// Same as convertCalendarToFYMonth but with more descriptive name
  static int getFinancialYearMonthPosition(int calendarMonth) {
    return convertCalendarToFYMonth(calendarMonth);
  }

  /// Generate financial year months up to current month
  /// Returns list of calendar months from April to current month in FY
  static List<int> getFinancialYearMonthsUpToCurrent() {
    final now = DateTime.now();
    final currentFYStartYear = getCurrentFinancialYearStartYear();

    final monthsToShow = <int>[];
    final fyMonths = getFinancialYearMonths();

    for (final calendarMonth in fyMonths) {
      final year =
          calendarMonth >= 4 ? currentFYStartYear : currentFYStartYear + 1;
      final monthDate = DateTime(year, calendarMonth, 1);

      // Include months up to and including current month
      if (monthDate.isBefore(now) ||
          (monthDate.year == now.year && monthDate.month == now.month)) {
        monthsToShow.add(calendarMonth);
      } else {
        break;
      }
    }

    return monthsToShow;
  }

  /// Generate DateTime objects for financial year months up to current month
  static List<DateTime> getFinancialYearDatesUpToCurrent() {
    final currentFYStartYear = getCurrentFinancialYearStartYear();
    final monthsToShow = getFinancialYearMonthsUpToCurrent();

    return monthsToShow.map((calendarMonth) {
      final year =
          calendarMonth >= 4 ? currentFYStartYear : currentFYStartYear + 1;
      return DateTime(year, calendarMonth, 1);
    }).toList();
  }
}
