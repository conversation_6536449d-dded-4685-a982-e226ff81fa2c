import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'dart:typed_data';
import 'package:intl/intl.dart';
import '../../../../controller/homectrl.dart';
import '../../../../models/user_model.dart';

class SubsidiaryLedgerMemberExport {
  Future<Uint8List> buildMemberPDF({
    required UserModel user,
    required int year,
    required HomeCtrl ctrl,
  }) async {
    // old var names + old style
    final userMonthlyRecords = ctrl.usermonthly
        .where((record) =>
            record.cpfNo == user.cpfNo && record.selectedyear == year)
        .toList()
      ..sort((a, b) => (a.selectedmonth ?? 0).compareTo(b.selectedmonth ?? 0));

    final obSubs = user.obSubs ?? 0;
    final obShares = user.obShares ?? 0;
    final obLtLoan = user.obLt ?? 0;
    final obStLoan = user.obSt ?? 0;

    final totals = <String, num>{
      'loanPaidLtTotal': 0,
      'loanPaidStTotal': 0,
      'subscriptionTotal': 0,
      'ltInstallmentTotal': 0,
      'stInstallmentTotal': 0,
      'interestTotal': 0,
      'totalAmtPaidTotal': 0,
    };

    num recShares = 0;

    for (final record in userMonthlyRecords) {
      totals['loanPaidLtTotal'] =
          totals['loanPaidLtTotal']! + (record.loanPaidLt ?? 0);
      totals['loanPaidStTotal'] =
          totals['loanPaidStTotal']! + (record.loanPaidst ?? 0);
      totals['subscriptionTotal'] =
          totals['subscriptionTotal']! + record.subscriptionPaid;
      totals['ltInstallmentTotal'] =
          totals['ltInstallmentTotal']! + record.longTermInstalmentPaid;
      totals['stInstallmentTotal'] =
          totals['stInstallmentTotal']! + record.shortTermInstalmentPaid;
      totals['interestTotal'] =
          totals['interestTotal']! + (record.interest ?? 0);
      totals['totalAmtPaidTotal'] =
          totals['totalAmtPaidTotal']! + (record.installmentRec ?? 0);

      recShares += (record.shareValue ?? 0);
    }

    String getMonthName(int monthNum) {
      const monthNames = [
        '',
        'JANUARY',
        'FEBRUARY',
        'MARCH',
        'APRIL',
        'MAY',
        'JUNE',
        'JULY',
        'AUGUST',
        'SEPTEMBER',
        'OCTOBER',
        'NOVEMBER',
        'DECEMBER'
      ];
      if (monthNum >= 1 && monthNum <= 12) return monthNames[monthNum];
      return 'UNKNOWN';
    }

    String getBalanceAsOnDate() {
      final now = DateTime.now();
      const months = [
        '',
        'JAN',
        'FEB',
        'MAR',
        'APR',
        'MAY',
        'JUN',
        'JUL',
        'AUG',
        'SEP',
        'OCT',
        'NOV',
        'DEC'
      ];
      return 'BALANCE AS ON ${now.day}-${months[now.month]}-${now.year}';
    }

    final dateFormatter = DateFormat('dd-MM-yyyy');

    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: pw.EdgeInsets.all(20),
        build: (context) => pw.Container(
          padding: const pw.EdgeInsets.all(12),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Center(
                child: pw.Text(
                  'FCI EMPLOYEES CO OPERATIVE CREDIT SOCIETY : BARODA',
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
              pw.Center(
                child: pw.Text(
                  'MEMBER SUBSIDIARY LEDGER FOR THE FINANCIAL YEAR $year-${year + 1}',
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 11,
                  ),
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Center(
                child: pw.Text(
                  'NAME OF SOCIETY MEMBER : ${user.name.toUpperCase()}',
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 13,
                    color: PdfColors.black,
                  ),
                ),
              ),
              pw.SizedBox(height: 12),

              // OB BOX
              pw.Align(
                alignment: pw.Alignment.center,
                child: pw.Container(
                  width: 200,
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey),
                  ),
                  padding:
                      const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                  child: pw.Column(
                    children: [
                      _row('OB SUBS :', obSubs),
                      _row('OB SHARES :', obShares),
                      _row('OBLT :', obLtLoan),
                      _row('OBST :', obStLoan),
                    ],
                  ),
                ),
              ),
              pw.SizedBox(height: 15),

              pw.Table.fromTextArray(
                headers: [
                  'MONTH',
                  'DATE',
                  'OBLT',
                  'OBST',
                  'LOAN PAID LT',
                  'LOAN PAID ST',
                  'SUBSCRIPTION',
                  'LT INSTALLMENT',
                  'ST INSTALLMENT',
                  'INTEREST',
                  'TOTAL AMT PAID',
                  'LTCB',
                  'STCB'
                ],
                data: [
                  for (final record in userMonthlyRecords)
                    [
                      getMonthName(record.installmentRecDate?.month ?? 0),
                      record.installmentRecDate != null
                          ? dateFormatter.format(record.installmentRecDate!)
                          : '-',
                      (record.obLt ?? 0).toString(),
                      (record.obSt ?? 0).toString(),
                      (record.loanPaidLt ?? 0).toString(),
                      (record.loanPaidst ?? 0).toString(),
                      record.subscriptionPaid.toString(),
                      record.longTermInstalmentPaid.toString(),
                      record.shortTermInstalmentPaid.toString(),
                      (record.interest ?? 0).toString(),
                      (record.installmentRec ?? 0).toString(),
                      (record.ltCb ?? 0).toString(),
                      (record.stCb ?? 0).toString(),
                    ],
                  [
                    'TOTAL',
                    '',
                    '',
                    '',
                    totals['loanPaidLtTotal'].toString(),
                    totals['loanPaidStTotal'].toString(),
                    totals['subscriptionTotal'].toString(),
                    totals['ltInstallmentTotal'].toString(),
                    totals['stInstallmentTotal'].toString(),
                    totals['interestTotal'].toString(),
                    totals['totalAmtPaidTotal'].toString(),
                    '',
                    '',
                  ]
                ],
                cellAlignment: pw.Alignment.center,
                headerStyle:
                    pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 8),
                cellStyle: pw.TextStyle(fontSize: 8),
                border: pw.TableBorder.all(color: PdfColors.grey300),
              ),

              pw.SizedBox(height: 15),

              pw.Container(
                width: double.infinity,
                child: pw.Column(
                  children: [
                    pw.Text('SUMMARY',
                        style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold, fontSize: 10)),
                    pw.Table.fromTextArray(
                      headers: [
                        'PARTICULAR',
                        'OB',
                        'RECEIVED DURING THE YEAR',
                        'PAID DURING THE YEAR',
                        getBalanceAsOnDate()
                      ],
                      data: [
                        [
                          'SUBSCRIPTION',
                          obSubs.toString(),
                          totals['subscriptionTotal'].toString(),
                          '0',
                          (obSubs + (totals['subscriptionTotal'] ?? 0))
                              .toString()
                        ],
                        [
                          'SHARE',
                          obShares.toString(),
                          recShares.toString(),
                          '0',
                          (obShares + recShares).toString()
                        ],
                        [
                          'LT LOAN',
                          obLtLoan.toString(),
                          totals['ltInstallmentTotal'].toString(),
                          totals['loanPaidLtTotal'].toString(),
                          ((obLtLoan +
                                      (totals['ltInstallmentTotal'] ?? 0) -
                                      (totals['loanPaidLtTotal'] ?? 0))
                                  .abs())
                              .toString()
                        ],
                        [
                          'ST LOAN',
                          obStLoan.toString(),
                          totals['stInstallmentTotal'].toString(),
                          totals['loanPaidStTotal'].toString(),
                          ((obStLoan +
                                      (totals['stInstallmentTotal'] ?? 0) -
                                      (totals['loanPaidStTotal'] ?? 0))
                                  .abs())
                              .toString()
                        ],
                      ],
                      cellAlignment: pw.Alignment.center,
                      headerStyle: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold, fontSize: 8),
                      cellStyle: pw.TextStyle(fontSize: 8),
                      border: pw.TableBorder.all(color: PdfColors.grey300),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );

    return Uint8List.fromList(await pdf.save());
  }

  pw.Widget _row(String label, num value) => pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(label, style: pw.TextStyle(fontSize: 10)),
          pw.Text(value.toString(),
              style:
                  pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold)),
        ],
      );
}
