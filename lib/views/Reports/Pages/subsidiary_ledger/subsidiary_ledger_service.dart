import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../../controller/homectrl.dart';
import '../../../../models/user_model.dart';
import 'subsidiary_ledger_page.dart';

class SubsidiaryLedgerService {
  List<PlutoRow> generateSubsidiaryLedgerRows(
      List<UserModel> users, HomeCtrl ctrl, int? selectedYear) {
    final sortedUsers = List<UserModel>.from(users)
      ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

    final rows = sortedUsers.asMap().entries.map((entry) {
      final index = entry.key;
      final user = entry.value;

      final doName = ctrl.districtoffice.firstWhereOrNull(
        (element) => element.docId == user.districtoffice,
      );

      final Map<int, dynamic> monthlyData = {};

      for (int i = 0; i < 12; i++) {
        final monthNum = getMonthNum(i);
        final match = ctrl.usermonthly.firstWhereOrNull(
          (e) =>
              e.cpfNo == user.cpfNo &&
              e.selectedmonth == monthNum &&
              ((monthNum >= 3 && e.selectedyear == selectedYear) ||
                  (monthNum < 3 && e.selectedyear == selectedYear! + 1)),
        );
        monthlyData[monthNum] = match;
      }

      /*   for (int i = 0; i < 12; i++) {
        final monthNum = getMonthNum(i);
        final match = ctrl.usermonthly.firstWhereOrNull(
          (e) =>
              e.cpfNo == user.cpfNo &&
              e.selectedmonth == monthNum &&
              e.selectedyear == selectedYear,
        );
        monthlyData[monthNum] = match;
      } */

      final Map<String, PlutoCell> monthCells = {};
      for (int i = 0; i < 12; i++) {
        final monthNum = getMonthNum(i);
        final data = monthlyData[monthNum];

        monthCells.addAll({
          'subs_$monthNum': PlutoCell(value: formatCeil(data?.subs)),
          'lt_installment_$monthNum':
              PlutoCell(value: formatCeil(data?.ltInstallment)),
          'st_installment_$monthNum':
              PlutoCell(value: formatCeil(data?.stInstallment)),
          'interest_$monthNum': PlutoCell(value: formatCeil(data?.interest)),
          'penalty_$monthNum': PlutoCell(value: formatCeil(data?.penalty)),
        });
      }

      return PlutoRow(
        cells: {
          'sr_no': PlutoCell(value: index + 1),
          'cpf_no': PlutoCell(value: user.cpfNo),
          'name': PlutoCell(value: user.name.toUpperCase()),
          'district_office': PlutoCell(value: doName?.name ?? ""),
          'ob_subscription': PlutoCell(value: user.obSubs),
          'ob_lt': PlutoCell(value: user.obLt),
          'ob_st': PlutoCell(value: user.obSt),
          'ob_shares': PlutoCell(value: user.obShares),
          ...monthCells,
        },
      );
    }).toList();

    final Map<String, double> totalMap = {};
    for (final row in rows) {
      row.cells.forEach((key, cell) {
        if (key.startsWith('subs_') ||
            key.startsWith('lt_installment_') ||
            key.startsWith('st_installment_') ||
            key.startsWith('interest_') ||
            key.startsWith('penalty_')) {
          final val = double.tryParse(cell.value.toString()) ?? 0.0;
          totalMap[key] = (totalMap[key] ?? 0.0) + val;
        }
      });
    }

    final totalRow = PlutoRow(
      cells: {
        'sr_no': PlutoCell(value: ''),
        'cpf_no': PlutoCell(value: ''),
        'name': PlutoCell(value: 'TOTAL'),
        'district_office': PlutoCell(value: ''),
        'ob_subscription': PlutoCell(value: ''),
        'ob_lt': PlutoCell(value: ''),
        'ob_st': PlutoCell(value: ''),
        'ob_shares': PlutoCell(value: ''),
        ...generateTotalCells(totalMap),
      },
    );

    rows.add(totalRow);
    return rows;
  }
}
