// ignore_for_file: use_build_context_synchronously, deprecated_member_use, avoid_web_libraries_in_flutter

import 'dart:convert';
import 'package:csv/csv.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class YearlySubsPage extends StatefulWidget {
  const YearlySubsPage({super.key});

  @override
  State<YearlySubsPage> createState() => _YearlySubsPageState();
}

class _YearlySubsPageState extends State<YearlySubsPage> {
  late PlutoGridStateManager stateManager;

  bool isLoading = false;
  bool csvExportIsLoading = false;
  bool pdfExportIsLoading = false;
  bool onSaveIsLoading = false;

  String? yearlySubSelectedFinancialYear;
  int? yearlySubSelectedYear; // Keep for backward compatibility

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  List<PlutoRow> rows = [];

  final List<PlutoColumnGroup> columnGroups = [
    PlutoColumnGroup(
      title: 'PLACE OF POSTING',
      fields: ['district_office'],
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumnGroup(
      title: 'OPENING BALANCE',
      fields: ['ob_subscription'],
    ),
    ...getDynamicMonthNumbers().map((monthNum) {
      final prefix = monthShortName(monthNum);
      final isColored = monthNum % 2 == 0;
      return PlutoColumnGroup(
        title: prefix,
        fields: ['paid_$monthNum', 'rec_$monthNum', 'monthly_total_$monthNum'],
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      );
    }),
  ];

  final List<PlutoColumn> columns = [
    PlutoColumn(
      title: 'SR. NO',
      field: 'sr_no',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'CPF NO.',
      field: 'cpf_no',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'NAME OF MEMBER',
      field: 'name',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'DISTRICT OFFICE',
      field: 'district_office',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'OB SUBSCRIPTION',
      field: 'ob_subscription',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
    ...getDynamicMonthNumbers().expand((monthNum) {
      final prefix = monthShortName(monthNum);
      final isColored = monthNum % 2 == 0;
      return [
        PlutoColumn(
            title: '$prefix PAID',
            field: 'paid_$monthNum',
            type: PlutoColumnType.number(),
            enableEditingMode: false,
            backgroundColor: isColored ? Color(0xffC9E9D2) : null),
        PlutoColumn(
            title: '$prefix REC',
            field: 'rec_$monthNum',
            type: PlutoColumnType.number(),
            enableEditingMode: false,
            backgroundColor: isColored ? Color(0xffC9E9D2) : null),
        PlutoColumn(
            title: '$prefix MONTHLY TOTAL',
            field: 'monthly_total_$monthNum',
            type: PlutoColumnType.number(),
            enableEditingMode: false,
            backgroundColor: isColored ? Color(0xffC9E9D2) : null),
      ];
    }),
    PlutoColumn(
      title: 'PROGRESSIVE TOTAL',
      field: 'progressive_total',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title:
          'INTEREST AMOUNT (${Get.find<HomeCtrl>().settings?.subscriptionInterest ?? 0}%)',
      field: 'interest_amount',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    yearlySubSelectedFinancialYear = TheFinancialYear.getCurrentFinancialYear();
    yearlySubSelectedYear = TheFinancialYear.getCurrentFinancialYearStartYear();
  }

  void loadData(HomeCtrl ctrl) {
    setState(() => isLoading = true);
    try {
      insertYearlySubsRows(ctrl.users);
    } catch (e) {
      showCtcAppSnackBar(context, "Error Loading Data");
      debugPrint('Error loading data: ${e.toString()}');
    } finally {
      setState(() => isLoading = false);
    }
  }

  void insertYearlySubsRows(List<UserModel> users) {
    try {
      // Clear existing rows
      rows.clear();

      // Sort users by CPF number
      final sortedUsers = List<UserModel>.from(users)
        ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

      // Generate rows for each user
      rows = sortedUsers.asMap().entries.map((entry) {
        final index = entry.key;
        final user = entry.value;
        final obSubs = user.obSubs ?? 0;

        // Get district office name
        final doName = Get.find<HomeCtrl>().districtoffice.firstWhereOrNull(
              (element) => element.docId == user.districtoffice,
            );

        final ctrl = Get.find<HomeCtrl>();
        // final num obSubs = 0; // opening balance os subs

        // Create cells for each month
        final Map<String, PlutoCell> monthCells = {};

        num progressiveTotal = 0;
        num lastMonthlyTotal = obSubs;

        // Get financial year months up to current month
        final availableMonths =
            TheFinancialYear.getFinancialYearMonthsUpToCurrent();
        final allFYMonths = TheFinancialYear.getFinancialYearMonths();

        for (final monthNum in allFYMonths) {
          // Check if this month is available (up to current month in FY)
          if (!availableMonths.contains(monthNum)) {
            monthCells.addAll({
              'paid_$monthNum': PlutoCell(value: 0),
              'rec_$monthNum': PlutoCell(value: ''),
              'monthly_total_$monthNum': PlutoCell(value: ''),
            });
            continue;
          }

          // Get monthly data
          final monthlyData = Get.find<HomeCtrl>().usermonthly.firstWhereOrNull(
                (e) =>
                    e.cpfNo == user.cpfNo &&
                    e.selectedmonth == monthNum &&
                    e.selectedyear == yearlySubSelectedYear,
              );

          final num received = monthlyData?.subs ?? 0;

          if (received > 0) {
            lastMonthlyTotal += received;
          }

          progressiveTotal = lastMonthlyTotal;

          monthCells.addAll({
            'paid_$monthNum': PlutoCell(value: 0),
            'rec_$monthNum': PlutoCell(value: received),
            'monthly_total_$monthNum': PlutoCell(value: lastMonthlyTotal),
          });
        }

        num subsInt =
            num.tryParse(ctrl.settings?.subscriptionInterest ?? '0') ?? 0;
        final interestAmount = progressiveTotal * subsInt / 1200;

        // Create and return PlutoRow
        return PlutoRow(
          cells: {
            'sr_no': PlutoCell(value: (index + 1).toString()),
            'cpf_no': PlutoCell(value: user.cpfNo),
            'name': PlutoCell(value: user.name.toUpperCase()),
            'district_office': PlutoCell(value: doName?.name ?? ''),
            'ob_subscription': PlutoCell(value: obSubs),
            ...monthCells,
            'progressive_total':
                PlutoCell(value: progressiveTotal.toStringAsFixed(2)),
            'interest_amount':
                PlutoCell(value: interestAmount.toStringAsFixed(0)),
          },
        );
      }).toList();

      // Calculate totals
      final Map<String, double> totalMap = {};
      for (final row in rows) {
        row.cells.forEach((key, cell) {
          if (key.startsWith('paid_') ||
              key.startsWith('rec_') ||
              key.startsWith('monthly_total_') ||
              key == 'progressive_total' ||
              key == 'interest_amount') {
            final value = double.tryParse(cell.value.toString()) ?? 0.0;
            totalMap[key] = (totalMap[key] ?? 0.0) + value;
          }
        });
      }

      // Add total row
      final totalRow = PlutoRow(
        cells: {
          'sr_no': PlutoCell(value: ''),
          'cpf_no': PlutoCell(value: ''),
          'name': PlutoCell(value: 'TOTAL'),
          'district_office': PlutoCell(value: ''),
          'ob_subscription': PlutoCell(value: ''),
          ...totalMap.map((key, value) => MapEntry(
                key,
                PlutoCell(value: value.toStringAsFixed(2)),
              )),
        },
      );

      rows.add(totalRow);

      stateManager.removeAllRows();
      stateManager.appendRows(rows);
      stateManager.notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
      showCtcAppSnackBar(context, 'Error inserting rows');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        value: yearlySubSelectedYear,
                        decoration: InputDecoration(
                            hintText: "Select Year",
                            constraints: const BoxConstraints(
                                maxWidth: 150, maxHeight: 45),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5))),
                        items: List.generate(
                          yearList.length,
                          (index) {
                            return DropdownMenuItem(
                              value: yearList[index],
                              child: Text(yearList[index].toString()),
                            );
                          },
                        ),
                        onChanged: (value) async {
                          setState(() => yearlySubSelectedYear = value);
                          try {
                            insertYearlySubsRows(ctrl.users);
                          } catch (e) {
                            debugPrint(e.toString());
                            showCtcAppSnackBar(context, 'Error loading data');
                          }
                        })),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    csvExportIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async => await exportCsv(),
                            buttonName: 'Export to CSV',
                          ),
                    SizedBox(width: 5),
                    pdfExportIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async => await exportPdf(),
                            buttonName: 'Export to PDF',
                          ),
                    // SizedBox(width: 5),
                    // onSaveIsLoading
                    //     ? CircularProgressIndicator()
                    //     : CustomHeaderButton(
                    //         onPressed: () async => await yearlySubsOnSave(ctrl),
                    //         buttonName: 'SAVE')
                  ],
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text(
                  "YEARLY SUBSCRIPTION REPORT FOR THE YEAR $yearlySubSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                // height: size.height, //  - 30,
                child: PlutoGrid(
                  columnGroups: columnGroups,
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    stateManager.setShowColumnFilter(true);
                    loadData(ctrl);
                    stateManager.notifyListeners();
                    stateManager.notifyListenersOnPostFrame();
                  },
                  onChanged: (PlutoGridOnChangedEvent event) {},
                  configuration: const PlutoGridConfiguration(
                      scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: true,
                          isAlwaysShown: true,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging)),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Future<void> exportCsv() async {
    try {
      setState(() => csvExportIsLoading = true);

      // Create CSV data
      List<List<dynamic>> csvData = [];

      // Add headers
      List<String> headers = [
        'Sr. No',
        'CPF No.',
        'Name',
        'District Office',
        'OB Subscription',
      ];

      // Add month headers
      getDynamicMonthNumbers().forEach((monthNum) {
        final prefix = monthShortName(monthNum);
        headers.add('$prefix PAID');
        headers.add('$prefix REC');
        headers.add('$prefix MONTHLY TOTAL');
      });

      // Add progressive and interest at the end
      headers.add('Progressive Total');
      headers.add('Interest Amount');

      csvData.add(headers);

      // Add row data
      for (PlutoRow row in rows) {
        if (row.cells['name']?.value == 'TOTAL') {
          // Add empty row before total
          csvData.add([]);
        }

        List<dynamic> rowData = [];

        // Basic info
        rowData.add(row.cells['sr_no']?.value ?? '');
        rowData.add(row.cells['cpf_no']?.value ?? '');
        rowData.add(row.cells['name']?.value ?? '');
        rowData.add(row.cells['district_office']?.value ?? '');

        // OB Subscription
        var obValue = row.cells['ob_subscription']?.value ?? '';
        if (obValue is num) obValue = obValue.toStringAsFixed(2);
        rowData.add(obValue);

        // Monthly values
        getDynamicMonthNumbers().forEach((monthNum) {
          var paidValue = row.cells['paid_$monthNum']?.value ?? '';
          var recValue = row.cells['rec_$monthNum']?.value ?? '';
          var monthlyTotalValue =
              row.cells['monthly_total_$monthNum']?.value ?? '';

          if (paidValue is num) paidValue = paidValue.toStringAsFixed(2);
          if (recValue is num) recValue = recValue.toStringAsFixed(2);
          if (monthlyTotalValue is num) {
            monthlyTotalValue = monthlyTotalValue.toStringAsFixed(2);
          }

          rowData.add(paidValue);
          rowData.add(recValue);
          rowData.add(monthlyTotalValue);
        });

        // Progressive total and interest at the end
        var progressiveValue = row.cells['progressive_total']?.value ?? '';
        var interestValue = row.cells['interest_amount']?.value ?? '';

        if (progressiveValue is num) {
          progressiveValue = progressiveValue.toStringAsFixed(2);
        }
        if (interestValue is num) {
          interestValue = interestValue.toStringAsFixed(2);
        }

        rowData.add(progressiveValue);
        rowData.add(interestValue);

        csvData.add(rowData);
      }

      // Convert to CSV string with UTF8 BOM for Excel compatibility
      final csv = const ListToCsvConverter().convert(csvData);
      final csvWithBOM = '\uFEFF$csv';

      // Create blob for web download
      final bytes = utf8.encode(csvWithBOM);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download = 'yearly_subscription_$yearlySubSelectedYear.csv';

      html.document.body!.children.add(anchor);
      anchor.click();

      // Cleanup
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      showCtcAppSnackBar(context, 'CSV file has been downloaded');
      setState(() => csvExportIsLoading = false);
    } catch (e) {
      showCtcAppSnackBar(context, 'Export Failed');
      setState(() => csvExportIsLoading = false);

      debugPrint('CSV Export Error: $e');
    } finally {
      setState(() => csvExportIsLoading = false);
    }
  }

  Future<void> exportPdf() async {
    try {
      setState(() => pdfExportIsLoading = true);

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat:
              PdfPageFormat(4000, PdfPageFormat.a3.height, marginAll: 40),
          orientation: pw.PageOrientation.landscape,
          build: (pw.Context context) {
            final months = getDynamicMonthNumbers();

            // Helper for consistent cell padding and style
            pw.Widget cell(String text,
                {bool isHeader = false, double fontSize = 9}) {
              return pw.Container(
                padding:
                    const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                child: pw.Text(
                  text,
                  style: pw.TextStyle(
                    fontSize: fontSize,
                    fontWeight:
                        isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
                  ),
                ),
              );
            }

            return [
              pw.Text(
                'YEARLY SUBSCRIPTION REPORT FOR THE YEAR $yearlySubSelectedYear',
                style:
                    pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),
              pw.Table(
                border: pw.TableBorder.all(width: 0.3),
                defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
                // columnWidths: {
                //   0: pw.FixedColumnWidth(10), // Sr. No
                //   1: pw.FixedColumnWidth(10), // CPF No.
                //   2: pw.FixedColumnWidth(10), // Name
                //   3: pw.FixedColumnWidth(10), // District Office
                //   4: pw.FixedColumnWidth(10), // OB Subscription
                //   for (int i = 0; i < months.length * 3; i++)
                //     5 + i:
                //         pw.FixedColumnWidth(10), // Each month: PAID, REC, TOTAL
                //   5 + months.length * 3:
                //       pw.FixedColumnWidth(10), // Progressive Total
                //   6 + months.length * 3:
                //       pw.FixedColumnWidth(10), // Interest Amount
                // },
                children: [
                  // Column Headers
                  pw.TableRow(
                    decoration: pw.BoxDecoration(color: PdfColors.grey300),
                    children: [
                      cell('Sr. No', isHeader: true),
                      cell('CPF No.', isHeader: true),
                      cell('Name', isHeader: true),
                      cell('District Office', isHeader: true),
                      cell('OB Subscription', isHeader: true),
                      ...months.expand((monthNum) => [
                            cell('${monthShortName(monthNum)} PAID',
                                isHeader: true),
                            cell('${monthShortName(monthNum)} REC',
                                isHeader: true),
                            cell('${monthShortName(monthNum)} TOTAL',
                                isHeader: true),
                          ]),
                      cell('Progressive Total', isHeader: true),
                      cell('Interest Amount', isHeader: true),
                    ],
                  ),

                  // Data Rows
                  ...rows.map((row) {
                    getVal(String field) {
                      final val = row.cells[field]?.value;
                      if (val is num) return val.toStringAsFixed(0);
                      return val?.toString() ?? '';
                    }

                    return pw.TableRow(
                      children: [
                        cell(getVal('sr_no')),
                        cell(getVal('cpf_no')),
                        cell(getVal('name')),
                        cell(getVal('district_office')),
                        cell(getVal('ob_subscription')),
                        ...months.expand((monthNum) => [
                              cell(getVal('paid_$monthNum')),
                              cell(getVal('rec_$monthNum')),
                              cell(getVal('monthly_total_$monthNum')),
                            ]),
                        cell(getVal('progressive_total')),
                        cell(getVal('interest_amount')),
                      ],
                    );
                  }),
                ],
              ),
            ];
          },
        ),
      );

      final bytes = await pdf.save();
      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download =
            'yearly_subscription_${yearlySubSelectedYear}_${DateTime.now().millisecondsSinceEpoch}.pdf';

      html.document.body!.children.add(anchor);
      anchor.click();
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      showCtcAppSnackBar(context, 'PDF file has been downloaded');
      setState(() => pdfExportIsLoading = false);
    } catch (e) {
      showCtcAppSnackBar(context, 'PDF Export Failed');
      setState(() => pdfExportIsLoading = false);

      debugPrint('PDF Export Error: $e');
    } finally {
      setState(() => pdfExportIsLoading = false);
    }
  }
}
