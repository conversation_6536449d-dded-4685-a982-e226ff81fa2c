import 'package:flutter_test/flutter_test.dart';
import 'package:foodcorp_admin/shared/const.dart';

void main() {
  group('TheFinancialYear Tests', () {
    test('getCurrentFinancialYearStartYear should return correct year for April-March FY', () {
      // Test cases for different months
      // Note: These tests assume we can mock DateTime.now(), but for now we'll test the logic
      
      // April should start new FY
      final aprilDate = DateTime(2024, 4, 1);
      final aprilFY = aprilDate.month >= 4 ? aprilDate.year : aprilDate.year - 1;
      expect(aprilFY, equals(2024));
      
      // March should be end of previous FY
      final marchDate = DateTime(2024, 3, 31);
      final marchFY = marchDate.month >= 4 ? marchDate.year : marchDate.year - 1;
      expect(marchFY, equals(2023));
      
      // January should be part of previous FY
      final janDate = DateTime(2024, 1, 15);
      final janFY = janDate.month >= 4 ? janDate.year : janDate.year - 1;
      expect(janFY, equals(2023));
    });

    test('getFinancialYearMonths should return correct sequence', () {
      final months = TheFinancialYear.getFinancialYearMonths();
      expect(months, equals([4, 5, 6, 7, 8, 9, 10, 11, 12, 1, 2, 3]));
    });

    test('convertCalendarToFYMonth should convert correctly', () {
      // April (4) should be FY month 1
      expect(TheFinancialYear.convertCalendarToFYMonth(4), equals(1));
      
      // May (5) should be FY month 2
      expect(TheFinancialYear.convertCalendarToFYMonth(5), equals(2));
      
      // March (3) should be FY month 12
      expect(TheFinancialYear.convertCalendarToFYMonth(3), equals(12));
      
      // January (1) should be FY month 10
      expect(TheFinancialYear.convertCalendarToFYMonth(1), equals(10));
      
      // December (12) should be FY month 9
      expect(TheFinancialYear.convertCalendarToFYMonth(12), equals(9));
    });

    test('convertFYToCalendarMonth should convert correctly', () {
      // FY month 1 should be April (4)
      expect(TheFinancialYear.convertFYToCalendarMonth(1), equals(4));
      
      // FY month 2 should be May (5)
      expect(TheFinancialYear.convertFYToCalendarMonth(2), equals(5));
      
      // FY month 12 should be March (3)
      expect(TheFinancialYear.convertFYToCalendarMonth(12), equals(3));
      
      // FY month 10 should be January (1)
      expect(TheFinancialYear.convertFYToCalendarMonth(10), equals(1));
      
      // FY month 9 should be December (12)
      expect(TheFinancialYear.convertFYToCalendarMonth(9), equals(12));
    });

    test('getFinancialYearMonthName should return correct names', () {
      // FY month 1 should be April
      expect(TheFinancialYear.getFinancialYearMonthName(1), equals('April'));
      expect(TheFinancialYear.getFinancialYearMonthName(1, short: true), equals('APR'));
      
      // FY month 12 should be March
      expect(TheFinancialYear.getFinancialYearMonthName(12), equals('March'));
      expect(TheFinancialYear.getFinancialYearMonthName(12, short: true), equals('MAR'));
      
      // FY month 6 should be September
      expect(TheFinancialYear.getFinancialYearMonthName(6), equals('September'));
      expect(TheFinancialYear.getFinancialYearMonthName(6, short: true), equals('SEP'));
    });

    test('conversion functions should be inverse of each other', () {
      for (int calendarMonth = 1; calendarMonth <= 12; calendarMonth++) {
        final fyMonth = TheFinancialYear.convertCalendarToFYMonth(calendarMonth);
        final backToCalendar = TheFinancialYear.convertFYToCalendarMonth(fyMonth);
        expect(backToCalendar, equals(calendarMonth), 
               reason: 'Calendar month $calendarMonth -> FY month $fyMonth -> Calendar month $backToCalendar');
      }
      
      for (int fyMonth = 1; fyMonth <= 12; fyMonth++) {
        final calendarMonth = TheFinancialYear.convertFYToCalendarMonth(fyMonth);
        final backToFY = TheFinancialYear.convertCalendarToFYMonth(calendarMonth);
        expect(backToFY, equals(fyMonth),
               reason: 'FY month $fyMonth -> Calendar month $calendarMonth -> FY month $backToFY');
      }
    });

    test('getFinancialYearForDate should return correct FY string', () {
      // April 1, 2024 should be start of FY 2024-25
      final april2024 = DateTime(2024, 4, 1);
      expect(TheFinancialYear.getFinancialYearForDate(april2024), equals('2024-25'));
      
      // March 31, 2024 should be end of FY 2023-24
      final march2024 = DateTime(2024, 3, 31);
      expect(TheFinancialYear.getFinancialYearForDate(march2024), equals('2023-24'));
      
      // January 15, 2024 should be part of FY 2023-24
      final jan2024 = DateTime(2024, 1, 15);
      expect(TheFinancialYear.getFinancialYearForDate(jan2024), equals('2023-24'));
    });

    test('isDateInFinancialYear should work correctly', () {
      // Test FY 2023-24 (April 1, 2023 to March 31, 2024)
      final fyStartYear = 2023;
      
      // April 1, 2023 should be in FY 2023-24
      expect(TheFinancialYear.isDateInFinancialYear(DateTime(2023, 4, 1), fyStartYear), isTrue);
      
      // March 31, 2024 should be in FY 2023-24
      expect(TheFinancialYear.isDateInFinancialYear(DateTime(2024, 3, 31), fyStartYear), isTrue);
      
      // January 15, 2024 should be in FY 2023-24
      expect(TheFinancialYear.isDateInFinancialYear(DateTime(2024, 1, 15), fyStartYear), isTrue);
      
      // March 31, 2023 should NOT be in FY 2023-24
      expect(TheFinancialYear.isDateInFinancialYear(DateTime(2023, 3, 31), fyStartYear), isFalse);
      
      // April 1, 2024 should NOT be in FY 2023-24
      expect(TheFinancialYear.isDateInFinancialYear(DateTime(2024, 4, 1), fyStartYear), isFalse);
    });

    test('error handling for invalid inputs', () {
      expect(() => TheFinancialYear.convertCalendarToFYMonth(0), throwsArgumentError);
      expect(() => TheFinancialYear.convertCalendarToFYMonth(13), throwsArgumentError);
      expect(() => TheFinancialYear.convertFYToCalendarMonth(0), throwsArgumentError);
      expect(() => TheFinancialYear.convertFYToCalendarMonth(13), throwsArgumentError);
    });
  });
}
