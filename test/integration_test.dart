import 'package:flutter_test/flutter_test.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/methods.dart';

void main() {
  group('Financial Year Integration Tests', () {
    test('getDynamicMonthNumbers should return financial year months up to current', () {
      final dynamicMonths = getDynamicMonthNumbers();
      final expectedMonths = TheFinancialYear.getFinancialYearMonthsUpToCurrent();
      
      expect(dynamicMonths, equals(expectedMonths));
      
      // Verify months are in financial year order (April first)
      if (dynamicMonths.isNotEmpty) {
        // First month should be April (4) if we're past April, or the first available month
        final fyMonths = TheFinancialYear.getFinancialYearMonths();
        expect(dynamicMonths.first, equals(fyMonths.first));
        
        // Verify all returned months are in correct FY sequence
        for (int i = 0; i < dynamicMonths.length - 1; i++) {
          final currentMonth = dynamicMonths[i];
          final nextMonth = dynamicMonths[i + 1];
          final currentIndex = fyMonths.indexOf(currentMonth);
          final nextIndex = fyMonths.indexOf(nextMonth);
          
          expect(nextIndex, equals(currentIndex + 1), 
                 reason: 'Months should be in FY sequence: $currentMonth -> $nextMonth');
        }
      }
    });

    test('Financial year utilities should be consistent across the system', () {
      // Test that all utility functions return consistent results
      final fyMonths = TheFinancialYear.getFinancialYearMonths();
      final dynamicMonths = getDynamicMonthNumbers();
      
      // Dynamic months should be a subset of FY months
      for (final month in dynamicMonths) {
        expect(fyMonths.contains(month), isTrue, 
               reason: 'Dynamic month $month should be in FY months');
      }
      
      // Test month name consistency
      for (final month in fyMonths) {
        final fyPosition = TheFinancialYear.convertCalendarToFYMonth(month);
        final monthName = TheFinancialYear.getFinancialYearMonthName(fyPosition);
        final shortName = TheFinancialYear.getFinancialYearMonthName(fyPosition, short: true);
        
        expect(monthName.isNotEmpty, isTrue);
        expect(shortName.isNotEmpty, isTrue);
        expect(shortName.length, equals(3));
      }
    });

    test('Financial year boundaries should work correctly', () {
      // Test April 1st (start of FY)
      final april1 = DateTime(2024, 4, 1);
      expect(TheFinancialYear.getFinancialYearForDate(april1), equals('2024-25'));
      expect(TheFinancialYear.isDateInFinancialYear(april1, 2024), isTrue);
      expect(TheFinancialYear.isDateInFinancialYear(april1, 2023), isFalse);
      
      // Test March 31st (end of FY)
      final march31 = DateTime(2024, 3, 31);
      expect(TheFinancialYear.getFinancialYearForDate(march31), equals('2023-24'));
      expect(TheFinancialYear.isDateInFinancialYear(march31, 2023), isTrue);
      expect(TheFinancialYear.isDateInFinancialYear(march31, 2024), isFalse);
      
      // Test January (middle of FY)
      final jan15 = DateTime(2024, 1, 15);
      expect(TheFinancialYear.getFinancialYearForDate(jan15), equals('2023-24'));
      expect(TheFinancialYear.isDateInFinancialYear(jan15, 2023), isTrue);
      expect(TheFinancialYear.isDateInFinancialYear(jan15, 2024), isFalse);
    });

    test('Month conversion should handle all edge cases', () {
      // Test all calendar months
      final testCases = {
        1: 10,  // January -> FY month 10
        2: 11,  // February -> FY month 11
        3: 12,  // March -> FY month 12
        4: 1,   // April -> FY month 1
        5: 2,   // May -> FY month 2
        6: 3,   // June -> FY month 3
        7: 4,   // July -> FY month 4
        8: 5,   // August -> FY month 5
        9: 6,   // September -> FY month 6
        10: 7,  // October -> FY month 7
        11: 8,  // November -> FY month 8
        12: 9,  // December -> FY month 9
      };
      
      for (final entry in testCases.entries) {
        final calendarMonth = entry.key;
        final expectedFYMonth = entry.value;
        
        final actualFYMonth = TheFinancialYear.convertCalendarToFYMonth(calendarMonth);
        expect(actualFYMonth, equals(expectedFYMonth),
               reason: 'Calendar month $calendarMonth should convert to FY month $expectedFYMonth');
        
        // Test reverse conversion
        final backToCalendar = TheFinancialYear.convertFYToCalendarMonth(actualFYMonth);
        expect(backToCalendar, equals(calendarMonth),
               reason: 'FY month $actualFYMonth should convert back to calendar month $calendarMonth');
      }
    });

    test('Financial year month names should be correct', () {
      final expectedNames = {
        1: 'April',     // FY month 1 = April
        2: 'May',       // FY month 2 = May
        3: 'June',      // FY month 3 = June
        4: 'July',      // FY month 4 = July
        5: 'August',    // FY month 5 = August
        6: 'September', // FY month 6 = September
        7: 'October',   // FY month 7 = October
        8: 'November',  // FY month 8 = November
        9: 'December',  // FY month 9 = December
        10: 'January',  // FY month 10 = January
        11: 'February', // FY month 11 = February
        12: 'March',    // FY month 12 = March
      };
      
      final expectedShortNames = {
        1: 'APR', 2: 'MAY', 3: 'JUN', 4: 'JUL', 5: 'AUG', 6: 'SEP',
        7: 'OCT', 8: 'NOV', 9: 'DEC', 10: 'JAN', 11: 'FEB', 12: 'MAR',
      };
      
      for (final entry in expectedNames.entries) {
        final fyMonth = entry.key;
        final expectedName = entry.value;
        final expectedShort = expectedShortNames[fyMonth]!;
        
        expect(TheFinancialYear.getFinancialYearMonthName(fyMonth), equals(expectedName));
        expect(TheFinancialYear.getFinancialYearMonthName(fyMonth, short: true), equals(expectedShort));
      }
    });

    test('System should never show June in April position', () {
      // This was the original bug - June (month 6) showing in April position
      final fyMonths = TheFinancialYear.getFinancialYearMonths();
      
      // April should be first in FY sequence
      expect(fyMonths.first, equals(4), reason: 'April (4) should be first month in FY');
      
      // June should be 3rd in FY sequence (April=1st, May=2nd, June=3rd)
      expect(fyMonths[2], equals(6), reason: 'June (6) should be 3rd month in FY sequence');
      
      // Verify FY month positions
      expect(TheFinancialYear.convertCalendarToFYMonth(4), equals(1)); // April = FY month 1
      expect(TheFinancialYear.convertCalendarToFYMonth(6), equals(3)); // June = FY month 3
      
      // June should never appear in April's position (FY month 1)
      expect(TheFinancialYear.convertFYToCalendarMonth(1), equals(4), 
             reason: 'FY month 1 should always be April (4), never June (6)');
    });
  });
}
